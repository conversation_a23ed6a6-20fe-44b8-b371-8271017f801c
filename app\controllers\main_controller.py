from PyQt5.QtCore import QObject, pyqtSignal, QDate
from PyQt5.QtWidgets import QIn<PERSON>D<PERSON>og, QMessageBox, QDialog
from app.services.task_service import TaskService
from app.models.task import Task as TaskModel # To use TaskModel.repeat_display_text
from datetime import datetime, date
from app.controllers.task_controller import TaskController
from app.controllers.backlog_controller import Backlog<PERSON>ontroller
from app.controllers.filter_controller import FilterController
from app.controllers.format_controller import FormatController
from app.controllers.comment_controller import CommentController
from app.controllers.reminder_controller import ReminderController
from app.controllers.report_controller import ReportController
from app.utils.theme_manager import set_current_theme, apply_theme, get_current_theme

class MainController(QObject):
    task_list_updated = pyqtSignal(list)
    project_list_updated = pyqtSignal(list)
    task_detail_updated = pyqtSignal(dict)
    filter_label_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, app, view=None):
        super().__init__()
        self.app = app
        self.task_service = TaskService()
        self.view = view
        self.filter_controller = FilterController(self)
        self.task_controller = TaskController(self)
        self.format_controller = FormatController()
        self.backlog_controller = BacklogController(self)
        # Connect filter controller signals to our signals to forward them
        self.filter_controller.task_list_updated.connect(self.task_list_updated)
        self.filter_controller.filter_label_updated.connect(self.filter_label_updated)
        self.filter_controller.error_occurred.connect(self.error_occurred)        # Connect task controller signals to our signals to forward them
        self.task_controller.error_occurred.connect(self.error_occurred)
        self.task_controller.task_detail_updated.connect(self.task_detail_updated)
        self.backlog_controller.error_occurred.connect(self.error_occurred)
        # self.load_initial_data()

    def change_theme(self, theme_name):
        """Changes the application theme and saves the setting."""
        try:
            set_current_theme(theme_name)
            apply_theme(self.app)
            QMessageBox.information(self.view, "テーマ変更", "テーマが変更されました。一部の変更を完全に適用するには、アプリケーションの再起動が必要な場合があります。")
        except Exception as e:
            self.error_occurred.emit(f"テーマの変更に失敗しました: {e}")

    def get_current_theme(self):
        """Gets the current theme name."""
        return get_current_theme()

    def _parse_date(self, date_str_or_qdate):
        return self.format_controller.parse_date(date_str_or_qdate)

    def _format_qdate_to_datetime(self, q_date: QDate) -> datetime | None:
        return self.format_controller.format_qdate_to_datetime(q_date)

    def _format_task_for_view(self, task_orm_object: TaskModel):
        return self.format_controller.format_task_for_view(task_orm_object)

    def _format_task_details_for_view(self, task_orm: TaskModel):
        return self.format_controller.format_task_details_for_view(task_orm)
        
    def load_initial_data(self):
        print("Controller: Loading initial data...")
        try:
            # Default to "すべてのタスク" on initial load - delegated to filter controller
            self.filter_controller.filter_all_tasks()

            projects = self.task_service.get_all_project_names()
            self.project_list_updated.emit(projects)
            
        except Exception as e:
            self.error_occurred.emit(f"初期データの読み込みに失敗しました: {e}")
            print(f"Error loading initial data: {e}")

    # Delegate to filter controller
    def _apply_filters_and_update_view(self):
        """Delegates to filter controller to apply current filters and update the view."""
        self.filter_controller._apply_filters_and_update_view()

    # Filter method delegations to filter controller
    def filter_today_tasks(self):
        print("Controller: Delegating filter_today_tasks to FilterController")
        self.filter_controller.filter_today_tasks()

    def filter_all_tasks(self):
        print("Controller: Delegating filter_all_tasks to FilterController")
        self.filter_controller.filter_all_tasks()

    def filter_incomplete_tasks(self):
        print("Controller: Delegating filter_incomplete_tasks to FilterController")
        self.filter_controller.filter_incomplete_tasks()

    def filter_completed_tasks(self):
        print("Controller: Delegating filter_completed_tasks to FilterController")
        self.filter_controller.filter_completed_tasks()
        
    def filter_by_project(self, project_name_from_view: str):
        print(f"Controller: Delegating filter_by_project to FilterController for '{project_name_from_view}'")
        self.filter_controller.filter_by_project(project_name_from_view)

    def add_task_dialog(self):
        """Delegate to task controller"""
        print("Controller: Delegating add_task_dialog to TaskController")
        return self.task_controller.add_task_dialog()

    def select_task(self, task_id: int):
        return self.task_controller.select_task(task_id)

    def handle_task_completion_change(self, task_id: int, is_completed: bool):
        """Delegate to task controller"""
        print("Controller: Delegating handle_task_completion_change to TaskController")
        return self.task_controller.handle_task_completion_change(task_id, is_completed)
        
    def edit_task_dialog(self, task_id: int):
        """Delegate to task controller"""
        print("Controller: Delegating edit_task_dialog to TaskController")
        return self.task_controller.edit_task_dialog(task_id)

    def change_task_status_dialog(self, task_id: int):
        """Delegate to task controller"""
        print("Controller: Delegating change_task_status_dialog to TaskController")
        return self.task_controller.change_task_status_dialog(task_id)

    def delete_task_confirmation(self, task_id: int, task_title: str = None):
        """Delegate to task controller"""
        print("Controller: Delegating delete_task_confirmation to TaskController")
        return self.task_controller.delete_task_confirmation(task_id, task_title)
        
    def delete_multiple_tasks_confirmation(self, task_ids: list):
        """Delegate to task controller"""
        print("Controller: Delegating delete_multiple_tasks_confirmation to TaskController")
        return self.task_controller.delete_multiple_tasks_confirmation(task_ids)

    def add_comment_and_save_actuals(self, task_id: int, comment_text: str,
                                     actual_time: float, 
                                     actual_start_qdate: QDate, 
                                     actual_end_qdate: QDate):
        """Delegate to task controller"""
        print("Controller: Delegating add_comment_and_save_actuals to TaskController")
        return self.task_controller.add_comment_and_save_actuals(task_id, comment_text, actual_time, actual_start_qdate, actual_end_qdate)

    def edit_comment_dialog(self, task_id: int, comment_id: int):
        """Delegate to task controller"""
        print("Controller: Delegating edit_comment_dialog to TaskController")
        return self.task_controller.edit_comment_dialog(task_id, comment_id)

    def delete_comment_confirmation(self, task_id: int, comment_id: int):
        """Delegate to task controller"""
        print("Controller: Delegating delete_comment_confirmation to TaskController")
        return self.task_controller.delete_comment_confirmation(task_id, comment_id)
    
    # --- Project List Context Menu Actions ---
    def rename_project(self, old_project_name: str):
        if old_project_name == "なし" or not old_project_name: # Cannot rename "No Project"
            return
        
        new_project_name, ok = QInputDialog.getText(self.view, 'プロジェクト名変更', 
                                                    f"プロジェクト '{old_project_name}' の新しい名前:", 
                                                    text=old_project_name)
        if ok and new_project_name and new_project_name.strip() != old_project_name:
            new_project_name = new_project_name.strip()
            if not new_project_name: # Prevent renaming to empty string
                self.error_occurred.emit("プロジェクト名は空にできません。")
                return

            existing_projects = self.task_service.get_all_project_names()
            if new_project_name in existing_projects:
                self.error_occurred.emit(f"プロジェクト '{new_project_name}' は既に存在します。")
                return

            try:
                tasks_to_update = self.task_service.get_tasks_by_filter("プロジェクト", old_project_name)
                updated_count = 0
                for task_item in tasks_to_update:
                    updated = self.task_service.update_task(task_item.id, project=new_project_name)
                    if updated:
                        updated_count +=1
                
                print(f"Renamed project '{old_project_name}' to '{new_project_name}' for {updated_count} tasks.")
                self.load_initial_data() 
                
                # Check if we need to update the current filter
                filter_state = self.filter_controller.get_current_filter_state()
                if filter_state["current_active_filter"] == "プロジェクト" and filter_state["current_project_filter_name"] == old_project_name:
                    self.filter_controller.filter_by_project(new_project_name)

            except Exception as e:
                self.error_occurred.emit(f"プロジェクト名の変更に失敗しました: {e}")

    def delete_project(self, project_name: str):
        if project_name == "なし" or not project_name:
            return

        tasks_in_project = self.task_service.get_tasks_by_filter("プロジェクト", project_name)
        
        msg = f"プロジェクト '{project_name}' を削除しますか？"
        if tasks_in_project:
            msg = f"プロジェクト '{project_name}' には {len(tasks_in_project)} 件のタスクがあります。\n" \
                  f"プロジェクトを削除すると、これらのタスクのプロジェクト設定は「なし」になります。\n" \
                  f"続行しますか？"
        
        reply = QMessageBox.question(self.view, 'プロジェクト削除の確認', msg,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            try:
                updated_count = 0
                for task_item in tasks_in_project:
                    updated = self.task_service.update_task(task_item.id, project=None) # Set project to None
                    if updated:
                        updated_count +=1
                print(f"Removed project '{project_name}' from {updated_count} tasks.")
                self.load_initial_data()
                
                # Check if we need to update the current filter
                filter_state = self.filter_controller.get_current_filter_state()
                if filter_state["current_active_filter"] == "プロジェクト" and filter_state["current_project_filter_name"] == project_name:
                    self.filter_controller.filter_all_tasks()
            except Exception as e:
                self.error_occurred.emit(f"プロジェクトの削除に失敗しました: {e}")

    def open_backlog_settings(self):
        """Delegate to backlog controller"""
        print("Controller: Delegating open_backlog_settings to BacklogController")
        return self.backlog_controller.open_backlog_settings()

    def fetch_backlog_tasks(self):
        """Delegate to backlog controller"""
        print("Controller: Delegating fetch_backlog_tasks to BacklogController")
        return self.backlog_controller.fetch_backlog_tasks()

    # Placeholder for menu actions not yet fully implemented
    def open_reminder_settings(self): self.error_occurred.emit("未実装")
    def show_log_dialog(self): self.error_occurred.emit("未実装")
    def show_app_settings_dialog(self): self.error_occurred.emit("未実装")
    def debug_check_reminders(self): self.error_occurred.emit("未実装")
    def show_reminder_status_dialog(self): self.error_occurred.emit("未実装")
    def open_daily_report_tool(self):
        if self.view is None:
            self.error_occurred.emit("ビューが利用できません。")
            print("Error: MainController.view is not set.")
            return

        selected_task_ids = self.view.get_selected_task_ids()
        if not selected_task_ids:
            self.error_occurred.emit("レポートを生成するには、少なくとも1つのタスクを選択してください。")
            return

        try:
            selected_tasks = [self.task_service.get_task_by_id(task_id) for task_id in selected_task_ids]
            # Filter out any tasks that might not have been found
            selected_tasks = [task for task in selected_tasks if task is not None]

            if not selected_tasks:
                self.error_occurred.emit("選択されたタスクが見つかりませんでした。")
                return

            # Launch the report controller and dialog
            self.report_controller = ReportController(selected_tasks, parent_controller=self)
            self.report_controller.show_dialog()

        except Exception as e:
            self.error_occurred.emit(f"日報生成ツールの起動に失敗しました: {e}")
            print(f"Error opening daily report tool: {e}")

    def add_project_dialog(self): 
        self.error_occurred.emit("プロジェクトはタスク編集時に新しい名前を指定することで追加されます。")