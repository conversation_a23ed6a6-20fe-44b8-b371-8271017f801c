from PyQt5.QtWidgets import QDialog
from app.views.dialogs.reminder_dialog import ReminderSettingsDialog
from app.database.reminder_manager import get_reminder_settings, update_reminder_settings

class ReminderController:
    def __init__(self, parent=None):
        self.view = parent

    def show_settings_dialog(self):
        """
        Shows the reminder settings dialog, loads the current settings,
        and saves them if the user confirms.
        """
        # Get current settings from the database
        current_settings = get_reminder_settings()
        
        # Create and show the dialog, passing the current settings
        dialog = ReminderSettingsDialog(parent=self.view, initial_settings=current_settings)
        
        # If the user clicks "Save" (dialog is accepted)
        if dialog.exec_() == QDialog.Accepted:
            # Get the new settings from the dialog
            new_settings = dialog.get_settings()
            
            # Update the settings in the database
            update_reminder_settings(
                enabled=new_settings['enabled'],
                default_timing=new_settings['default_timing']
            )
