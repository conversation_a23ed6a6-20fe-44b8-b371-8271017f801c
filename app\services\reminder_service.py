import time
import threading
import logging
import os
from datetime import datetime, timedelta
from winotify import Notification, audio
from PyQt5.QtCore import QThread, pyqtSignal
from database.database_manager import get_db_connection


def load_custom_reminder_settings(tasks=None):
    """
    Loads custom reminder settings for all tasks.

    Args:
        tasks (list, optional): Task list object. Not used in the current implementation
                                but included for backward compatibility.

    Returns:
        dict: A dictionary where keys are task_ids and values are reminder_days.
              Example: {1: 5, 2: 3}
    """
    conn = get_db_connection()
    cursor = conn.cursor()
    # Assuming 'tasks' table has 'id' for task_id and 'reminder_days' for reminder days.
    # Fetches only tasks where reminder_enabled is true or not explicitly set (assuming default is true)
    # and reminder_days is not NULL.
    cursor.execute('''
        SELECT id, reminder_days 
        FROM tasks 
        WHERE (reminder_enabled = 1 OR reminder_enabled IS NULL) AND reminder_days IS NOT NULL
    ''')
    rows = cursor.fetchall()
    conn.close()

    reminder_settings = {}
    for row in rows:
        task_id = row['id']
        reminder_days = row['reminder_days']
        reminder_settings[task_id] = reminder_days
    
    return reminder_settings


def update_reminder_service_settings(reminder_service, tasks):
    """
    Updates the reminder service settings with custom reminder settings from tasks.
    
    Args:
        reminder_service: The reminder service instance to update
        tasks: The list of tasks with their reminder settings
    """
    if not reminder_service:
        logging.warning("Warning: Reminder service is None, cannot update settings")
        return
        
    # Load custom reminder settings from database
    custom_settings = load_custom_reminder_settings()
    
    # Update the settings in the reminder service
    if hasattr(reminder_service, 'settings'):
        # Create a new settings object with the same class
        new_settings = reminder_service.settings.__class__(
            enabled=reminder_service.settings.enabled,
            default_timing=reminder_service.settings.default_timing,
            custom_timing=custom_settings
        )
        
        # Update the service with the new settings
        reminder_service.update_settings(new_settings)
        logging.info(f"Updated reminder service with {len(custom_settings)} custom settings")


class ReminderSettings:
    """リマインダー設定を格納するクラス"""
    def __init__(self, enabled=True, default_timing=0, custom_timing=None):
        """
        Args:
            enabled (bool): リマインダー機能が有効かどうか
            default_timing (int): デフォルトのリマインド時間（日前）
            custom_timing (dict): タスクごとのカスタムリマインド時間 {task_id: days}
        """
        self.enabled = enabled
        self.default_timing = default_timing  # 日単位
        self.custom_timing = custom_timing or {}  # タスクIDをキーとするdict
        logging.info(f"リマインダー設定初期化: enabled={enabled}, default_timing={default_timing}日")
        
    def get_reminder_time(self, task_id, due_date):
        """タスクのリマインダー通知時間を計算する
        
        Args:
            task_id (int): タスクID
            due_date (datetime): タスクの期限日時
            
        Returns:
            datetime: リマインダー通知を行う日時
        """
        if not due_date:
            logging.debug(f"タスクID {task_id}: 期限日がありません")
            return None
            
        # タスク固有の設定があればそれを使う、なければデフォルト値を使う
        days = self.custom_timing.get(task_id, self.default_timing)
        
        # 期限の days 日前の日付を計算
        reminder_date = due_date - timedelta(days=days)
        
        # 日付のみを取り出し、その日の朝9時に設定（通知時刻を固定）
        result = datetime.combine(reminder_date.date(), datetime.strptime('09:00', '%H:%M').time())
        logging.debug(f"タスクID {task_id}: 期限日={due_date.date()}, リマインド日={result.date()} ({days}日前)")
        return result


class ReminderThread(QThread):
    """リマインダー処理を行うスレッド"""
    reminder_triggered = pyqtSignal(int)  # タスクIDを送信するシグナル
    
    def __init__(self, tasks, settings):
        """
        Args:
            tasks (list): タスクのリスト
            settings (ReminderSettings): リマインダー設定
        """
        super().__init__()
        self.tasks = tasks
        self.settings = settings
        self.running = True
        self.lock = threading.Lock()
        self.app_icon_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                          "resources", "app_icon.png")
        self._overdue_notified = False  # 過去のリマインドを通知したかどうかのフラグ
        
    def update_tasks(self, tasks):
        """タスクリストを更新する"""
        with self.lock:
            self.tasks = tasks
            
    def update_settings(self, settings):
        """設定を更新する"""
        with self.lock:
            self.settings = settings
            
    def stop(self):
        """スレッドを停止する"""
        self.running = False
        # wait()はブロッキング処理のためquitを使用し、タイムアウトを設定
        self.quit()
        self.wait(1000)  # 最大1秒待機
        
    def run(self):
        """リマインダーチェックのメインループ"""
        while self.running:
            try:
                self.check_reminders()
                # スリープを小さい単位に分割して終了チェックを頻繁に行う
                for _ in range(300):  # 5分間隔でチェック
                    if not self.running:
                        return
                    time.sleep(1)  # 1秒ずつスリープ
            except Exception as e:
                logging.error(f"リマインダーチェック中にエラーが発生しました: {str(e)}")
                # スリープを小さい単位に分割して終了チェックを頻繁に行う
                for _ in range(60):  # エラー発生時は1分待機
                    if not self.running:
                        return
                    time.sleep(1)  # 1秒ずつスリープ
                    
    def check_reminders(self):
        """リマインダーの条件を満たすタスクをチェックする"""
        if not self.settings.enabled:
            logging.debug("リマインダーは無効になっています。チェックをスキップします")
            return
            
        now = datetime.now()
        logging.info(f"リマインダーチェック実行中... 現在時刻: {now.strftime('%Y-%m-%d %H:%M:%S')}")
        logging.info(f"設定情報: 有効={self.settings.enabled}, デフォルト={self.settings.default_timing}日前, "
                     f"カスタム設定数={len(self.settings.custom_timing or {})}")
        
        with self.lock:
            checked_tasks = 0
            eligible_tasks = 0
            
            for task in self.tasks:
                checked_tasks += 1
                
                # 完了済みタスクはスキップ
                if task.completed:
                    continue
                    
                # 期限日がないタスクはスキップ
                if not task.due_date:
                    continue
                
                # due_dateが文字列の場合は日時型に変換
                due_date = task.due_date
                if isinstance(due_date, str):
                    try:
                        due_date = datetime.strptime(due_date, '%Y/%m/%d')
                        logging.debug(f"文字列日付を変換しました: {due_date} (フォーマット: %Y/%m/%d)")
                    except ValueError:
                        try:
                            due_date = datetime.strptime(due_date, '%Y-%m-%d')
                            logging.debug(f"文字列日付を変換しました: {due_date} (フォーマット: %Y-%m-%d)")
                        except ValueError:
                            logging.warning(f"タスク '{task.title}' (ID: {task.db_id}) の日付 '{due_date}' を解析できません")
                            continue  # 変換できない場合はスキップ
                
                # 日付型の場合は時刻情報を追加（その日の24時として扱う）
                if hasattr(due_date, 'date') and not hasattr(due_date, 'hour'):
                    due_date = datetime.combine(due_date.date(), datetime.max.time())
                
                # リマインド時間の計算
                reminder_time = self.settings.get_reminder_time(task.db_id, due_date)
                if not reminder_time:
                    logging.debug(f"タスク '{task.title}' (ID: {task.db_id}) のリマインド時間を計算できません")
                    continue
                
                eligible_tasks += 1
                logging.debug(f"タスク '{task.title}' (ID: {task.db_id}) のリマインド日: {reminder_time.date()}, 現在日: {now.date()}")
                
                # 日付が一致する場合にリマインド（同じ日であればいつでも通知）
                if now.date() == reminder_time.date():
                    days_to_due = (due_date.date() - now.date()).days
                    task_timing = self.settings.custom_timing.get(task.db_id, self.settings.default_timing)
                    logging.info(f"リマインド条件が一致しました！タスク '{task.title}' (ID: {task.db_id})")
                    logging.info(f"  期限日: {due_date.date()}, あと{days_to_due}日, リマインド設定: {task_timing}日前")
                    self.send_notification(task)
                    self.reminder_triggered.emit(task.db_id)
                else:
                    # リマインドされなかった理由をデバッグログに残す
                    days_diff = (reminder_time.date() - now.date()).days
                    if days_diff > 0:
                        logging.debug(f"タスク '{task.title}' (ID: {task.db_id}) は今日よりも {days_diff}日 後にリマインド予定です")
                    else:
                        logging.debug(f"タスク '{task.title}' (ID: {task.db_id}) は今日よりも {-days_diff}日 前にリマインド予定でした")
                        
                        # アプリ起動時の最初のチェックでのみ、過去のリマインドを一度だけ通知する
                        if -days_diff > 0 and -days_diff < 7 and not self._overdue_notified:
                            # リマインド日が7日以内の未通知タスクのみ通知
                            logging.info(f"過去のリマインドを検出: タスク '{task.title}' (ID: {task.db_id}), {-days_diff}日前")
                            self.send_notification(task)
                            self.reminder_triggered.emit(task.db_id)
            
            logging.info(f"リマインダーチェック完了: {checked_tasks}件チェック, {eligible_tasks}件が対象")
            
            # 過去リマインドの通知が終わったらフラグをセット
            if not self._overdue_notified:
                self._overdue_notified = True
                logging.info("初回起動時の過去リマインドチェックを完了しました")

    def send_notification(self, task):
        """Windows通知を送信する"""
        try:
            # アイコンパスの確認と修正
            icon_path = self.app_icon_path if os.path.exists(self.app_icon_path) else None
            logging.debug(f"通知アイコンパス: {icon_path}")
            
            # 期限日の表示形式を整える
            if hasattr(task.due_date, 'strftime'):
                due_date_str = task.due_date.strftime('%Y/%m/%d')
            else:
                due_date_str = str(task.due_date)
            
            # 現在日付から期限日までの日数を計算
            days_left = (task.due_date.date() - datetime.now().date()).days if hasattr(task.due_date, 'date') else "不明"
            days_str = f"あと{days_left}日" if isinstance(days_left, int) else days_left
            
            toast = Notification(
                app_id="タスク管理ツール",
                title=f"タスクリマインダー: {task.title}",
                msg=f"期限: {due_date_str} ({days_str})",
                icon=icon_path
            )
            
            # 通知音を設定
            toast.set_audio(audio.Reminder, loop=False)
            
            # 通知を表示
            logging.debug(f"Windows通知を表示します: {task.title} (期限: {due_date_str})")
            toast.show()
            
            # ログに記録
            logging.info(f"タスクID {task.db_id} '{task.title}' のリマインダー通知を送信しました")
            print(f"Windows通知を送信しました: {task.title} (期限: {due_date_str})")
            
            # フォールバック通知のセットアップ
            try:
                from PyQt5.QtWidgets import QApplication
                if QApplication.instance():
                    logging.debug("バックアップ通知の準備完了")
            except Exception as fallback_error:
                logging.debug(f"フォールバック通知の準備に失敗: {str(fallback_error)}")
                
        except Exception as e:
            logging.error(f"通知の送信に失敗しました: {str(e)}")
            import traceback
            logging.error(f"スタックトレース: {traceback.format_exc()}")
            
            # 追加の診断情報
            try:
                if 'icon_path' in locals():
                    logging.error(f"アイコンパスは存在しますか？: {os.path.exists(icon_path) if icon_path else 'アイコンなし'}")
                if 'toast' in locals():
                    logging.error(f"通知情報: app_id={toast.app_id}")
                print(f"通知に失敗しました。コンソールに表示: {task.title}")
            except Exception as inner_e:
                logging.error(f"診断中にさらにエラーが発生: {str(inner_e)}")


class ReminderService:
    """リマインダーサービス - タスク管理ツールとリマインダースレッドの間のインターフェース"""
    
    def __init__(self, tasks=None):
        """
        Args:
            tasks (list, optional): 初期タスクリスト
        """
        self.settings = ReminderSettings()
        self.tasks = tasks or []
        self.thread = None
        logging.info(f"ReminderService初期化: {len(self.tasks)}個のタスクをロード済み")
        
        # タスクの日付フォーマットを検証
        valid_due_dates = 0
        for task in self.tasks:
            if task.due_date:
                if isinstance(task.due_date, datetime):
                    valid_due_dates += 1
                elif isinstance(task.due_date, str):
                    logging.warning(f"文字列形式の日付があります: {task.title} - {task.due_date}")
        
        logging.info(f"有効な日付を持つタスク: {valid_due_dates}/{len(self.tasks)}")
        
    def start(self):
        """リマインダーサービスを開始する"""
        if self.thread and self.thread.isRunning():
            logging.info("リマインダーサービスは既に実行中です")
            return
            
        logging.info(f"リマインダーサービスを開始します: {len(self.tasks)}個のタスク")
        self.thread = ReminderThread(self.tasks, self.settings)
        
        # 初回のリマインドチェックを即座に実行（起動時に一度だけチェック）
        self.thread.check_reminders()
        logging.info("起動時のリマインドチェックを実行しました")
        
        # スレッドを開始してバックグラウンドでの定期チェックを開始
        self.thread.start()
        logging.info("リマインダーサービスを開始しました（バックグラウンドで定期チェックを実行）")
        
    def stop(self):
        """リマインダーサービスを停止する"""
        if self.thread and self.thread.isRunning():
            try:
                logging.info("リマインダーサービスを停止しています...")
                self.thread.stop()
                logging.info("リマインダーサービスを停止しました")
            except Exception as e:
                logging.error(f"リマインダーサービスの停止中にエラーが発生しました: {str(e)}")
                # スレッドが応答しない場合は強制終了
                self.thread.terminate()
                logging.info("リマインダーサービスを強制停止しました")
        
    def update_tasks(self, tasks):
        """タスクリストを更新する"""
        self.tasks = tasks
        # タスク日付の状態を出力
        valid_dates = sum(1 for task in tasks if task.due_date and isinstance(task.due_date, datetime))
        logging.info(f"タスクリストを更新: {len(tasks)}個のタスク, {valid_dates}個が有効な日付を持つ")
        
        if self.thread and self.thread.isRunning():
            self.thread.update_tasks(tasks)
            logging.info("リマインダースレッドのタスクリストを更新しました")
        
    def update_settings(self, settings):
        """設定を更新する"""
        self.settings = settings
        logging.info(f"リマインダー設定を更新: enabled={settings.enabled}, default_timing={settings.default_timing}")
        if self.thread and self.thread.isRunning():
            self.thread.update_settings(settings)
            logging.info("リマインダースレッドの設定を更新しました")
    
    def connect_reminder_signal(self, slot):
        """リマインダーシグナルにスロットを接続する"""
        if self.thread:
            self.thread.reminder_triggered.connect(slot)
            logging.info("リマインダー通知シグナルを接続しました")


# テスト用コード
if __name__ == '__main__':
    # ロギングの設定
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('reminder_service_test.log')
        ]
    )
    
    # シンプルなテスト用のタスククラス
    class TestTask:
        def __init__(self, db_id, title, due_date, completed=False):
            self.db_id = db_id
            self.title = title
            self.due_date = due_date
            self.completed = completed
            
    # 現在の日付から1日後、3日後、5日後のタスクを作成
    today = datetime.now()
    tasks = [
        TestTask(1, "テストタスク1", today + timedelta(days=1), False),
        TestTask(2, "テストタスク2", today + timedelta(days=3), False),
        TestTask(3, "テストタスク3", today + timedelta(days=5), False),
        TestTask(4, "テストタスク4（完了済み）", today + timedelta(days=2), True),
        TestTask(5, "テストタスク5（期限なし）", None, False),
        TestTask(6, "テストタスク6（過去の期限）", today - timedelta(days=2), False),
    ]
    
    # カスタムリマインド設定
    custom_settings = {
        1: 1,  # タスク1は1日前にリマインド
        2: 2,  # タスク2は2日前にリマインド
        3: 3,  # タスク3は3日前にリマインド
    }
    
    # リマインダーサービスを初期化
    service = ReminderService(tasks)
    settings = ReminderSettings(enabled=True, default_timing=1, custom_timing=custom_settings)
    service.update_settings(settings)
    
    # リマインダーシグナルのテスト用コールバック
    def on_reminder_triggered(task_id):
        print(f"リマインドを受信しました: タスクID {task_id}")
        # 実際のアプリケーションではここでUIの更新などを行う
    
    # シグナルを接続
    service.connect_reminder_signal(on_reminder_triggered)
    
    print("リマインダーサービスのテスト開始")
    print(f"登録タスク数: {len(tasks)}")
    print("リマインダーサービスを開始します...")
    
    try:
        # サービスを開始
        service.start()
        print("リマインダーサービスを開始しました（5秒後に停止します）")
        
        # テスト用に少し待機
        time.sleep(5)
    except KeyboardInterrupt:
        print("テストを中断しました")
    finally:
        # サービスを停止
        service.stop()
        print("リマインダーサービスを停止しました")
