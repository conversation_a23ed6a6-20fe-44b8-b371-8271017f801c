import os
import sys
import json
import importlib
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
                           QTextEdit, QPushButton, QLabel, QLineEdit, QToolButton, QGroupBox,
                           QSplitter, QMessageBox, QInputDialog, QWidget, QGridLayout,
                           QAbstractItemView)
from PyQt5.QtCore import Qt, QSize
from PyQt5.QtGui import QFont, QIcon

class TemplateEditorDialog(QDialog):
    """テンプレート編集ダイアログ"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("日報テンプレート編集")
        self.setMinimumSize(1000, 700)        # 現在のファイルの場所からパスを取得
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        # ルートディレクトリを取得して正しいパスを構築
        root_dir = os.path.abspath(os.path.join(self.current_dir, "..", "..", ""))
        self.templates_path = os.path.join(root_dir, "config", "custom_templates.json")
        print(f"テンプレートエディターのパス: {self.templates_path}")
        
        # テンプレート変数の定義
        self.template_variables = {
            'プロジェクト': ['project_name'],
            'タスク': ['title', 'issue_key', 'start_date', 'due_date', 'progress', 
                      'actual_start_date', 'actual_end_date', 'actual_period', 
                      'estimated_time', 'actual_time', 'comment', 'source', 'index'],
            'コメント': ['timestamp', 'text']
        }
        
        # テンプレートセクション
        self.template_sections = [
            'project_title', 'task_title', 'task_details', 
            'comment_history_header', 'comment_entry',
            'task_separator', 'project_separator'
        ]
        
        # 編集中のテンプレート
        self.current_template = None
        self.current_section = None
        
        # カスタムテンプレートのロード
        self.templates = self.load_templates()
        
        self.setup_ui()
        
    def setup_ui(self):
        """UIのセットアップ"""
        main_layout = QVBoxLayout(self)
        
        # 上部のツールバー
        toolbar_layout = QHBoxLayout()
        
        # 新規テンプレート作成ボタン
        self.new_template_btn = QPushButton("新規テンプレート")
        self.new_template_btn.clicked.connect(self.create_new_template)
        toolbar_layout.addWidget(self.new_template_btn)
        
        # テンプレート名入力フィールド
        self.template_name_edit = QLineEdit()
        self.template_name_edit.setPlaceholderText("テンプレート名")
        self.template_name_edit.setEnabled(False)
        toolbar_layout.addWidget(self.template_name_edit)
        
        # 保存ボタン
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_current_template)
        self.save_btn.setEnabled(False)
        toolbar_layout.addWidget(self.save_btn)
        
        # 削除ボタン
        self.delete_btn = QPushButton("削除")
        self.delete_btn.clicked.connect(self.delete_current_template)
        self.delete_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_btn)
        
        toolbar_layout.addStretch()
        
        main_layout.addLayout(toolbar_layout)
        
        # メイン編集エリア
        splitter = QSplitter(Qt.Horizontal)
        
        # 左側のテンプレートリスト
        template_list_group = QGroupBox("テンプレート")
        template_list_layout = QVBoxLayout()
        
        self.template_list = QListWidget()
        self.template_list.setSelectionMode(QAbstractItemView.SingleSelection)
        self.template_list.currentItemChanged.connect(self.on_template_selected)
        self.update_template_list()
        
        template_list_layout.addWidget(self.template_list)
        template_list_group.setLayout(template_list_layout)
        splitter.addWidget(template_list_group)
        
        # 中央の編集エリア（セクション選択を削除）
        middle_widget = QWidget()
        middle_layout = QVBoxLayout(middle_widget)
        
        # エディター
        editor_group = QGroupBox("テンプレート編集")
        editor_layout = QVBoxLayout()
        
        self.editor = QTextEdit()
        self.editor.setFont(QFont("Courier New", 10))
        self.editor.setLineWrapMode(QTextEdit.NoWrap)
        self.editor.textChanged.connect(self.on_editor_changed)
        
        editor_layout.addWidget(self.editor)
        editor_group.setLayout(editor_layout)
        middle_layout.addWidget(editor_group)
        
        splitter.addWidget(middle_widget)
        
        # 右側の変数と説明
        vars_widget = QWidget()
        vars_layout = QVBoxLayout(vars_widget)
        
        # 変数挿入ボタン
        vars_group = QGroupBox("テンプレート変数（クリックで挿入）")
        vars_group_layout = QVBoxLayout()
        
        for category, variables in self.template_variables.items():
            cat_group = QGroupBox(category)
            cat_layout = QGridLayout()
            
            for i, var in enumerate(variables):
                btn = QPushButton(f"{{{var}}}")
                btn.setToolTip(f"クリックで「{{{var}}}」を挿入")
                btn.clicked.connect(lambda checked, v=var: self.insert_variable(v))
                row, col = divmod(i, 2)
                cat_layout.addWidget(btn, row, col)
            
            cat_group.setLayout(cat_layout)
            vars_group_layout.addWidget(cat_group)
        
        vars_group_layout.addStretch()
        vars_group.setLayout(vars_group_layout)
        vars_layout.addWidget(vars_group)
        
        # 説明
        help_group = QGroupBox("説明")
        help_layout = QVBoxLayout()
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml("""
        <h3>テンプレート編集のヘルプ</h3>
        <p>このダイアログでは、日報の出力形式をカスタマイズできます。</p>
        <p><b>使い方：</b></p>
        <ol>
            <li>左側からテンプレートを選択するか、新規作成します</li>
            <li>編集エリアでJSONフォーマットのテンプレートを編集します</li>
            <li>右側の変数ボタンをクリックすると、その変数が挿入されます</li>
            <li>編集が完了したら「保存」ボタンをクリックします</li>
        </ol>
        <p><b>テンプレート構造：</b></p>
        <ul>
            <li><b>project_title</b> - プロジェクト名の表示形式（{project_name}を使用）</li>
            <li><b>task_title</b> - 各タスクのタイトル行の形式</li>
            <li><b>task_details</b> - タスクの詳細情報の表示形式</li>
            <li><b>comment_history_header</b> - コメント履歴セクションのヘッダー</li>
            <li><b>comment_entry</b> - 各コメントの表示形式</li>
            <li><b>task_separator</b> - タスク間の区切り文字</li>
            <li><b>project_separator</b> - プロジェクト間の区切り文字</li>
        </ul>
        <p><b>重要：</b></p>
        <ul>
            <li>テンプレートはJSONフォーマットで編集してください</li>
            <li>改行は\\nで表現し、インデントはスペースで指定します</li>
            <li>変数は{variable_name}の形式で記述します</li>
            <li>文字列内でダブルクォートを使用する場合は\\"でエスケープしてください</li>
        </ul>
        """)
        help_layout.addWidget(help_text)
        help_group.setLayout(help_layout)
        vars_layout.addWidget(help_group)
        
        splitter.addWidget(vars_widget)
        
        # スプリッターの初期サイズ比率を設定
        splitter.setSizes([200, 500, 300])
        
        main_layout.addWidget(splitter)
        
        # ボタンエリア
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("閉じる")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        main_layout.addLayout(button_layout)
    
    def load_templates(self):
        """テンプレートをJSONファイルから読み込む"""
        from config.report_templates import AVAILABLE_TEMPLATES
        
        # デフォルトテンプレートをコピー
        templates = AVAILABLE_TEMPLATES.copy()
        
        # カスタムテンプレートをロード（存在する場合）
        try:
            if os.path.exists(self.templates_path):
                with open(self.templates_path, 'r', encoding='utf-8') as f:
                    custom_templates = json.load(f)
                    templates.update(custom_templates)
        except Exception as e:
            print(f"テンプレート読み込みエラー: {e}")
        
        return templates
    
    def save_templates(self):
        """カスタムテンプレートをJSONファイルに保存"""
        
        # デフォルトテンプレートは保存しない
        custom_templates = {}
        for name, template in self.templates.items():
            if name not in ['デフォルト', 'カスタム1']:
                custom_templates[name] = template
        
        # JSONファイルに保存
        try:
            # ディレクトリが存在するか確認
            os.makedirs(os.path.dirname(self.templates_path), exist_ok=True)
            
            print(f"テンプレート保存先: {self.templates_path}")
            print(f"保存するテンプレート: {list(custom_templates.keys())}")
            
            with open(self.templates_path, 'w', encoding='utf-8') as f:
                json.dump(custom_templates, f, ensure_ascii=False, indent=4)
            
            print(f"{len(custom_templates)}個のカスタムテンプレートを保存しました")
            
            # 設定ファイルを再読み込みして保存したことを確認
            import importlib
            from config import report_templates
            if 'config.report_templates' in sys.modules:
                del sys.modules['config.report_templates']
            importlib.reload(report_templates)
            report_templates.load_custom_templates()
            
        except Exception as e:
            QMessageBox.critical(self, "保存エラー", f"テンプレートの保存に失敗しました。\n\nエラー詳細: {str(e)}")
            import traceback
            traceback.print_exc()
    
    def update_template_list(self):
        """テンプレートリストを更新"""
        self.template_list.clear()
        
        for name in sorted(self.templates.keys()):
            item = QListWidgetItem(name)
            item.setData(Qt.UserRole, name)
            
            # デフォルトテンプレートは編集不可
            if name in ['デフォルト', 'カスタム1']:
                item.setFlags(item.flags() & ~Qt.ItemIsEnabled)
            
            self.template_list.addItem(item)
    
    def on_template_selected(self, current, _previous):
        """テンプレートが選択されたときの処理"""
        if not current:
            self.current_template = None
            self.template_name_edit.setText("")
            self.template_name_edit.setEnabled(False)
            self.save_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            self.editor.setEnabled(False)
            return
        
        template_name = current.data(Qt.UserRole)
        self.current_template = template_name
        
        # デフォルトテンプレートは編集不可
        is_editable = template_name not in ['デフォルト', 'カスタム1']
        
        self.template_name_edit.setText(template_name)
        self.template_name_edit.setEnabled(is_editable)
        self.save_btn.setEnabled(is_editable)
        self.delete_btn.setEnabled(is_editable)
        self.editor.setEnabled(is_editable)
        
        # エディタにテンプレート全体をセット（JSONフォーマット）
        template = self.templates.get(template_name, {})

        # テンプレートをJSONフォーマットで表示
        import json
        content = json.dumps(template, ensure_ascii=False, indent=4)

        # エディタの更新を一時的にブロック
        self.editor.blockSignals(True)
        self.editor.setText(content)
        self.editor.blockSignals(False)
    
    def on_editor_changed(self):
        """エディタの内容が変更されたときの処理"""
        if not self.current_template:
            return

        # JSONフォーマットの内容をパース
        content = self.editor.toPlainText()
        try:
            import json
            template_data = json.loads(content)

            # テンプレートデータを更新
            if isinstance(template_data, dict):
                self.templates[self.current_template] = template_data
        except json.JSONDecodeError:
            # JSONパースエラーの場合は何もしない（編集中の可能性）
            pass
    
    def insert_variable(self, variable):
        """変数をエディタに挿入"""
        if self.editor.isEnabled():
            self.editor.insertPlainText(f"{{{variable}}}")
            self.editor.setFocus()

    def get_section_display_name(self, section):
        """セクション名の表示名を取得"""
        display_names = {
            'project_title': 'プロジェクトタイトル',
            'task_title': 'タスクタイトル',
            'task_details': 'タスク詳細',
            'comment_history_header': 'コメント履歴ヘッダー',
            'comment_entry': 'コメントエントリ',
            'task_separator': 'タスク区切り',
            'project_separator': 'プロジェクト区切り'
        }
        return display_names.get(section, section)
    
    def create_new_template(self):
        """新規テンプレートの作成"""
        # 新しいテンプレート名を取得
        name, ok = QInputDialog.getText(self, "新規テンプレート", "テンプレート名を入力してください:")
        
        if ok and name:
            # 同名のテンプレートがないかチェック
            if name in self.templates:
                QMessageBox.warning(self, "警告", f"「{name}」という名前のテンプレートは既に存在します。")
                return
            
            # デフォルトテンプレートをコピー
            from config.report_templates import DEFAULT_TEMPLATE
            self.templates[name] = DEFAULT_TEMPLATE.copy()
            
            # リストを更新して新しいテンプレートを選択
            self.update_template_list()
            
            # 新しいテンプレートを選択
            for i in range(self.template_list.count()):
                item = self.template_list.item(i)
                if item.data(Qt.UserRole) == name:
                    self.template_list.setCurrentItem(item)
                    break
    def save_current_template(self):
        """現在のテンプレートを保存"""
        if not self.current_template:
            return
        
        # テンプレート名が変更されているかチェック
        new_name = self.template_name_edit.text().strip()
        
        if not new_name:
            QMessageBox.warning(self, "警告", "テンプレート名を入力してください。")
            return
        
        # テンプレート構造を検証
        template_data = self.templates.get(self.current_template, {})
        missing_sections = []
        
        for required_section in self.template_sections:
            if required_section not in template_data or not template_data[required_section]:
                missing_sections.append(self.get_section_display_name(required_section))
        
        if missing_sections:
            reply = QMessageBox.question(
                self, 
                "テンプレート検証", 
                f"以下のセクションが空または未定義です:\n- {'\n- '.join(missing_sections)}\n\n保存を続行しますか？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return
        
        # project_title で使われる変数を検証
        project_title = template_data.get('project_title', '')
        if '{title}' in project_title or '{issue_key}' in project_title:
            reply = QMessageBox.question(
                self,
                "テンプレート検証",
                "プロジェクトタイトルセクションに、タスクレベルの変数 (title, issue_key など) が使用されています。\n"
                "これらの変数はプロジェクトレベルでは利用できず、エラーになる可能性があります。\n\n"
                "修正して保存しますか？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply == QMessageBox.Yes:
                # 不正な変数を置換
                template_data['project_title'] = template_data['project_title'].replace('{title}', '{project_name}')
                template_data['project_title'] = template_data['project_title'].replace('{issue_key}', '')
        
        if new_name != self.current_template:
            # 同名のテンプレートがないかチェック
            if new_name in self.templates:
                QMessageBox.warning(self, "警告", f"「{new_name}」という名前のテンプレートは既に存在します。")
                return
            
            # テンプレート名を変更
            self.templates[new_name] = self.templates[self.current_template]
            del self.templates[self.current_template]
            self.current_template = new_name
        
        # テンプレートをファイルに保存
        self.save_templates()
        
        # リストを更新して現在のテンプレートを選択
        self.update_template_list()
        
        # 保存したテンプレートを選択
        for i in range(self.template_list.count()):
            item = self.template_list.item(i)
            if item.data(Qt.UserRole) == self.current_template:
                self.template_list.setCurrentItem(item)
                break
        
        QMessageBox.information(self, "保存完了", "テンプレートを保存しました。")
    
    def delete_current_template(self):
        """現在のテンプレートを削除"""
        if not self.current_template:
            return
        
        # デフォルトテンプレートは削除不可
        if self.current_template in ['デフォルト', 'カスタム1']:
            QMessageBox.warning(self, "警告", "デフォルトテンプレートは削除できません。")
            return
        
        # 確認ダイアログ
        reply = QMessageBox.question(
            self, "削除確認", 
            f"テンプレート「{self.current_template}」を削除してもよろしいですか？",
            QMessageBox.Yes | QMessageBox.No, 
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # テンプレートを削除
            del self.templates[self.current_template]
            
            # テンプレートをファイルに保存
            self.save_templates()
            
            # リストを更新
            self.update_template_list()
            
            # 選択をクリア
            self.template_list.clearSelection()
            self.current_template = None
            
            QMessageBox.information(self, "削除完了", "テンプレートを削除しました。")
