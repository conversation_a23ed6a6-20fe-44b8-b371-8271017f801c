#!/usr/bin/env python3
"""
テンプレート編集機能のデモンストレーション
"""

import sys
import os
import json
from datetime import datetime

# プロジェクトのルートディレクトリをパスに追加
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from app.models.task import Task
from app.controllers.report_controller import ReportController

def create_demo_tasks():
    """デモ用のタスクを作成"""
    tasks = []
    
    # プロジェクトA のタスク
    task1 = Task()
    task1.id = 1
    task1.title = "ユーザー認証機能の実装"
    task1.description = "ログイン・ログアウト機能の開発"
    task1.status = "進行中"
    task1.project = "Webアプリケーション開発"
    task1.start_date = datetime(2024, 1, 15)
    task1.due_date = datetime(2024, 1, 25)
    task1.actual_start_date = datetime(2024, 1, 16)
    task1.estimated_time = 20.0
    task1.actual_time = 12.0
    task1.comments = []
    tasks.append(task1)
    
    task2 = Task()
    task2.id = 2
    task2.title = "データベース設計"
    task2.description = "ユーザー情報管理用のテーブル設計"
    task2.status = "完了"
    task2.project = "Webアプリケーション開発"
    task2.start_date = datetime(2024, 1, 10)
    task2.due_date = datetime(2024, 1, 15)
    task2.actual_start_date = datetime(2024, 1, 10)
    task2.actual_end_date = datetime(2024, 1, 14)
    task2.estimated_time = 8.0
    task2.actual_time = 6.0
    task2.comments = []
    tasks.append(task2)
    
    # プロジェクトB のタスク
    task3 = Task()
    task3.id = 3
    task3.title = "API仕様書作成"
    task3.description = "REST API の仕様書を作成"
    task3.status = "未着手"
    task3.project = "API開発"
    task3.start_date = datetime(2024, 1, 20)
    task3.due_date = datetime(2024, 1, 30)
    task3.estimated_time = 16.0
    task3.actual_time = 0.0
    task3.comments = []
    tasks.append(task3)
    
    return tasks

def create_custom_template():
    """カスタムテンプレートを作成してファイルに保存"""
    print("=== カスタムテンプレートの作成 ===")
    
    # 新しいカスタムテンプレート
    custom_template = {
        "project_title": "🚀 プロジェクト: {project_name}\n",
        "task_title": "  📋 {index}. [{issue_key}] {title}\n",
        "task_details": "      ⏰ 期間: {start_date} → {due_date}\n      📊 進捗: {progress}\n      ⏱️  実績: {actual_time:.1f}h / {estimated_time:.1f}h\n      💬 備考: {comment}\n",
        "comment_history_header": "      📝 履歴:\n",
        "comment_entry": "        • {timestamp}: {text}\n",
        "task_separator": "\n",
        "project_separator": "\n" + "="*50 + "\n\n"
    }
    
    # カスタムテンプレートファイルを読み込み
    custom_templates_path = os.path.join("config", "custom_templates.json")
    
    try:
        if os.path.exists(custom_templates_path):
            with open(custom_templates_path, 'r', encoding='utf-8') as f:
                existing_templates = json.load(f)
        else:
            existing_templates = {}
        
        # 新しいテンプレートを追加
        existing_templates["デモ用テンプレート"] = custom_template
        
        # ファイルに保存
        with open(custom_templates_path, 'w', encoding='utf-8') as f:
            json.dump(existing_templates, f, ensure_ascii=False, indent=4)
        
        print("✓ デモ用テンプレートを作成しました")
        return True
        
    except Exception as e:
        print(f"✗ テンプレート作成エラー: {e}")
        return False

def demonstrate_template_editing():
    """テンプレート編集機能のデモンストレーション"""
    print("\n=== テンプレート編集機能デモ ===")
    
    # PyQt5アプリケーションを初期化
    app = QApplication(sys.argv)
    
    try:
        # デモ用タスクを作成
        demo_tasks = create_demo_tasks()
        print(f"✓ {len(demo_tasks)}個のデモタスクを作成しました")
        
        # ReportControllerを初期化
        controller = ReportController(demo_tasks)
        print("✓ ReportControllerを初期化しました")
        
        # 利用可能なテンプレートを表示
        template_combo = controller.view.template_combo
        print(f"\n利用可能なテンプレート ({template_combo.count()}個):")
        for i in range(template_combo.count()):
            template_name = template_combo.itemText(i)
            print(f"  {i+1}. {template_name}")
        
        # 各テンプレートでレポートを生成
        print("\n=== 各テンプレートでのレポート生成例 ===")
        
        for i in range(min(3, template_combo.count())):  # 最初の3つのテンプレートをテスト
            template_combo.setCurrentIndex(i)
            template_name = template_combo.itemText(i)
            
            print(f"\n--- {template_name} テンプレート ---")
            
            # レポートの最初の部分を表示
            report_text = controller.view.preview_text.toPlainText()
            lines = report_text.split('\n')
            
            for j, line in enumerate(lines[:8]):  # 最初の8行を表示
                if line.strip():
                    print(f"  {line}")
                elif j < 7:  # 空行も表示（最後の空行は除く）
                    print()
            
            if len(lines) > 8:
                print("  ...")
        
        print(f"\n✓ テンプレート編集機能のデモが完了しました")
        print("✓ 実際のアプリケーションでは、「テンプレート編集」ボタンをクリックして")
        print("  テンプレートをカスタマイズできます")
        
        # ダイアログを表示（オプション）
        if len(sys.argv) > 1 and sys.argv[1] == "--show-dialog":
            print("\n日報ダイアログを表示します...")
            controller.show_dialog()
        
    except Exception as e:
        print(f"✗ デモ実行エラー: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

if __name__ == "__main__":
    print("テンプレート編集機能のデモンストレーションを開始します...")
    
    # カスタムテンプレートを作成
    if create_custom_template():
        # デモを実行
        demonstrate_template_editing()
    
    print("\n=== デモ完了 ===")
    print("\n使用方法:")
    print("1. メインアプリケーションを起動")
    print("2. タスクを選択して「日報生成」をクリック")
    print("3. 日報ダイアログで「テンプレート編集」ボタンをクリック")
    print("4. テンプレートをカスタマイズして保存")
    print("5. テンプレートを選択して日報を生成")
