#!/usr/bin/env python3
"""
インポートテスト - 提醒功能の依存関係をチェック
"""

import sys
import os

# プロジェクトのルートディレクトリをパスに追加
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """各モジュールのインポートをテスト"""
    
    print("=== インポートテスト開始 ===")
    
    try:
        print("1. 基本モジュールのテスト...")
        from datetime import datetime, timedelta
        print("   ✓ datetime")
        
        from PyQt5.QtCore import QObject, pyqtSignal
        print("   ✓ PyQt5.QtCore")
        
        from PyQt5.QtWidgets import QApplication
        print("   ✓ PyQt5.QtWidgets")
        
    except Exception as e:
        print(f"   ✗ 基本モジュールエラー: {e}")
        return False
    
    try:
        print("2. データベースモジュールのテスト...")
        from app.database.db import get_connection
        print("   ✓ app.database.db")
        
        from app.models.task import Task
        print("   ✓ app.models.task")
        
    except Exception as e:
        print(f"   ✗ データベースモジュールエラー: {e}")
        return False
    
    try:
        print("3. サービスモジュールのテスト...")
        from app.services.task_service import TaskService
        print("   ✓ app.services.task_service")
        
        from app.services.reminder_service import ReminderService, ReminderSettings
        print("   ✓ app.services.reminder_service")
        
    except Exception as e:
        print(f"   ✗ サービスモジュールエラー: {e}")
        return False
    
    try:
        print("4. コントローラーモジュールのテスト...")
        from app.controllers.main_controller import MainController
        print("   ✓ app.controllers.main_controller")
        
        from app.controllers.reminder_controller import ReminderController
        print("   ✓ app.controllers.reminder_controller")
        
    except Exception as e:
        print(f"   ✗ コントローラーモジュールエラー: {e}")
        return False
    
    try:
        print("5. リマインダー管理モジュールのテスト...")
        from app.database.reminder_manager import get_reminder_settings, update_reminder_settings
        print("   ✓ app.database.reminder_manager")
        
    except Exception as e:
        print(f"   ✗ リマインダー管理モジュールエラー: {e}")
        return False
    
    try:
        print("6. 外部ライブラリのテスト...")
        from winotify import Notification
        print("   ✓ winotify")
        
    except Exception as e:
        print(f"   ✗ winotifyエラー: {e}")
        print("   注意: winotifyがインストールされていない可能性があります")
        print("   pip install winotify でインストールしてください")
        return False
    
    print("=== すべてのインポートテスト成功 ===")
    return True

def test_basic_functionality():
    """基本機能のテスト"""
    
    print("\n=== 基本機能テスト開始 ===")
    
    try:
        print("1. TaskServiceの初期化...")
        from app.services.task_service import TaskService
        task_service = TaskService()
        print("   ✓ TaskService初期化成功")
        
        print("2. リマインダー設定の取得...")
        from app.database.reminder_manager import get_reminder_settings
        settings = get_reminder_settings()
        print(f"   ✓ 設定取得成功: {settings}")
        
        print("3. ReminderSettingsの初期化...")
        from app.services.reminder_service import ReminderSettings
        reminder_settings = ReminderSettings(
            enabled=settings.get('enabled', True),
            default_timing=settings.get('default_timing', 1)
        )
        print("   ✓ ReminderSettings初期化成功")
        
        print("4. 全タスクの取得...")
        all_tasks = task_service.get_tasks_by_filter("すべてのタスク")
        print(f"   ✓ タスク取得成功: {len(all_tasks)}件")
        
        print("5. ReminderServiceの初期化...")
        from app.services.reminder_service import ReminderService
        reminder_service = ReminderService(all_tasks)
        print("   ✓ ReminderService初期化成功")
        
    except Exception as e:
        print(f"   ✗ 基本機能テストエラー: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("=== 基本機能テスト成功 ===")
    return True

def main():
    """メイン関数"""
    print("リマインダー機能のインポートと基本機能をテストします...\n")
    
    # インポートテスト
    if not test_imports():
        print("\nインポートテストに失敗しました。")
        return 1
    
    # 基本機能テスト
    if not test_basic_functionality():
        print("\n基本機能テストに失敗しました。")
        return 1
    
    print("\n🎉 すべてのテストが成功しました！")
    print("リマインダー機能は正常に動作する準備ができています。")
    return 0

if __name__ == "__main__":
    sys.exit(main())
