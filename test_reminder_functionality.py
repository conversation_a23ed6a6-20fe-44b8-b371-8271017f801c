#!/usr/bin/env python3
"""
リマインダー機能のテストスクリプト

このスクリプトは以下をテストします：
1. リマインダーサービスの初期化
2. タスクのリマインダー設定
3. Windows Toast通知の表示
4. データベースとの連携
"""

import sys
import os
from datetime import datetime, timedelta
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QMessageBox
from PyQt5.QtCore import QTimer

# プロジェクトのルートディレクトリをパスに追加
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.reminder_service import ReminderService, ReminderSettings
from app.services.task_service import TaskService
from app.database.reminder_manager import get_reminder_settings, update_reminder_settings
from app.models.task import Task

class ReminderTestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("リマインダー機能テスト")
        self.setGeometry(100, 100, 400, 300)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Status label
        self.status_label = QLabel("リマインダーテストを開始してください")
        layout.addWidget(self.status_label)
        
        # Test buttons
        self.create_test_task_btn = QPushButton("テスト用タスクを作成")
        self.create_test_task_btn.clicked.connect(self.create_test_task)
        layout.addWidget(self.create_test_task_btn)
        
        self.test_reminder_service_btn = QPushButton("リマインダーサービスをテスト")
        self.test_reminder_service_btn.clicked.connect(self.test_reminder_service)
        layout.addWidget(self.test_reminder_service_btn)
        
        self.test_toast_btn = QPushButton("Toast通知をテスト")
        self.test_toast_btn.clicked.connect(self.test_toast_notification)
        layout.addWidget(self.test_toast_btn)
        
        self.test_db_settings_btn = QPushButton("データベース設定をテスト")
        self.test_db_settings_btn.clicked.connect(self.test_database_settings)
        layout.addWidget(self.test_db_settings_btn)
        
        self.manual_check_btn = QPushButton("手動リマインダーチェック")
        self.manual_check_btn.clicked.connect(self.manual_reminder_check)
        layout.addWidget(self.manual_check_btn)
        
        # Initialize services
        self.task_service = TaskService()
        self.reminder_service = None
        self.test_task_id = None
        
    def update_status(self, message):
        """ステータスラベルを更新"""
        self.status_label.setText(message)
        print(f"Status: {message}")
        
    def create_test_task(self):
        """テスト用のタスクを作成"""
        try:
            # 明日が期限のタスクを作成
            tomorrow = datetime.now() + timedelta(days=1)
            
            task = self.task_service.create_task(
                title="リマインダーテスト用タスク",
                description="このタスクはリマインダー機能のテスト用です",
                due_date=tomorrow,
                reminder_enabled=True,
                reminder_days=1  # 1日前にリマインド
            )
            
            if task:
                self.test_task_id = task.id
                self.update_status(f"テスト用タスクを作成しました (ID: {task.id})")
                QMessageBox.information(self, "成功", f"タスクID {task.id} を作成しました\n期限: {tomorrow.strftime('%Y/%m/%d')}")
            else:
                self.update_status("タスクの作成に失敗しました")
                QMessageBox.warning(self, "エラー", "タスクの作成に失敗しました")
                
        except Exception as e:
            self.update_status(f"タスク作成エラー: {e}")
            QMessageBox.critical(self, "エラー", f"タスク作成エラー: {e}")
    
    def test_reminder_service(self):
        """リマインダーサービスをテスト"""
        try:
            # 全タスクを取得
            all_tasks = self.task_service.get_tasks_by_filter("すべてのタスク")
            self.update_status(f"取得したタスク数: {len(all_tasks)}")
            
            # リマインダー設定を取得
            settings = get_reminder_settings()
            reminder_settings = ReminderSettings(
                enabled=settings.get('enabled', True),
                default_timing=settings.get('default_timing', 1)
            )
            
            # リマインダーサービスを初期化
            self.reminder_service = ReminderService(all_tasks)
            self.reminder_service.update_settings(reminder_settings)
            
            # シグナルを接続
            self.reminder_service.connect_reminder_signal(self.on_reminder_triggered)
            
            # サービスを開始
            if reminder_settings.enabled:
                self.reminder_service.start()
                self.update_status("リマインダーサービスを開始しました")
                QMessageBox.information(self, "成功", "リマインダーサービスが正常に開始されました")
            else:
                self.update_status("リマインダーサービスは無効になっています")
                QMessageBox.warning(self, "警告", "リマインダーサービスは設定で無効になっています")
                
        except Exception as e:
            self.update_status(f"リマインダーサービスエラー: {e}")
            QMessageBox.critical(self, "エラー", f"リマインダーサービスエラー: {e}")
    
    def test_toast_notification(self):
        """Toast通知をテスト"""
        try:
            from winotify import Notification
            
            # テスト通知を作成
            toast = Notification(
                app_id="TaskManager",
                title="リマインダーテスト",
                msg="これはテスト通知です。リマインダー機能が正常に動作しています。",
                duration="short"
            )
            
            # 通知を表示
            toast.show()
            self.update_status("Toast通知をテストしました")
            QMessageBox.information(self, "成功", "Toast通知が表示されました")
            
        except Exception as e:
            self.update_status(f"Toast通知エラー: {e}")
            QMessageBox.critical(self, "エラー", f"Toast通知エラー: {e}")
    
    def test_database_settings(self):
        """データベース設定をテスト"""
        try:
            # 現在の設定を取得
            current_settings = get_reminder_settings()
            self.update_status(f"現在の設定: {current_settings}")
            
            # 設定を更新
            success = update_reminder_settings(enabled=True, default_timing=1)
            
            if success:
                # 更新後の設定を確認
                updated_settings = get_reminder_settings()
                self.update_status(f"更新後の設定: {updated_settings}")
                QMessageBox.information(self, "成功", f"データベース設定テスト完了\n現在の設定: {updated_settings}")
            else:
                self.update_status("設定の更新に失敗しました")
                QMessageBox.warning(self, "エラー", "設定の更新に失敗しました")
                
        except Exception as e:
            self.update_status(f"データベース設定エラー: {e}")
            QMessageBox.critical(self, "エラー", f"データベース設定エラー: {e}")
    
    def manual_reminder_check(self):
        """手動でリマインダーチェックを実行"""
        try:
            if self.reminder_service and self.reminder_service.thread:
                self.reminder_service.thread.check_reminders()
                self.update_status("手動リマインダーチェックを実行しました")
                QMessageBox.information(self, "実行完了", "手動リマインダーチェックを実行しました")
            else:
                self.update_status("リマインダーサービスが開始されていません")
                QMessageBox.warning(self, "警告", "先にリマインダーサービスをテストしてください")
                
        except Exception as e:
            self.update_status(f"手動チェックエラー: {e}")
            QMessageBox.critical(self, "エラー", f"手動チェックエラー: {e}")
    
    def on_reminder_triggered(self, task_id):
        """リマインダーが発火した時の処理"""
        self.update_status(f"リマインダーが発火しました: タスクID {task_id}")
        print(f"Reminder triggered for task ID: {task_id}")

def main():
    """メイン関数"""
    app = QApplication(sys.argv)
    
    # テストウィンドウを作成
    window = ReminderTestWindow()
    window.show()
    
    print("リマインダー機能テストを開始します...")
    print("ウィンドウのボタンを使用してテストを実行してください")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
