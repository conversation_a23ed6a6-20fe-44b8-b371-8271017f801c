#!/usr/bin/env python3
"""
テンプレート編集機能のテストスクリプト
"""

import sys
import os
import json
from datetime import datetime

# プロジェクトのルートディレクトリをパスに追加
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.models.task import Task
from app.services.report_service import ReportService
from config.report_templates import AVAILABLE_TEMPLATES, load_custom_templates

def create_test_task():
    """テスト用のタスクを作成"""
    task = Task()
    task.id = 1
    task.title = "テストタスク"
    task.description = "これはテスト用のタスクです"
    task.status = "進行中"
    task.project = "テストプロジェクト"
    task.start_date = datetime(2024, 1, 1)
    task.due_date = datetime(2024, 1, 31)
    task.actual_start_date = datetime(2024, 1, 2)
    task.actual_end_date = None
    task.estimated_time = 8.0
    task.actual_time = 4.5
    task.comments = []
    return task

def test_template_format_preservation():
    """テンプレートの書式保持をテスト"""
    print("=== テンプレート書式保持テスト ===")
    
    # カスタムテンプレートを読み込み
    load_custom_templates()
    
    # テスト用タスクを作成
    test_task = create_test_task()
    
    # レポートサービスを初期化
    report_service = ReportService()
    
    # 各テンプレートでレポートを生成してテスト
    for template_name, template in AVAILABLE_TEMPLATES.items():
        print(f"\n--- {template_name} テンプレートのテスト ---")
        
        try:
            report = report_service.generate_report([test_task], template)
            print("✓ レポート生成成功")
            
            # 書式の確認
            lines = report.split('\n')
            has_indentation = any(line.startswith('    ') or line.startswith('　　') for line in lines)
            
            if has_indentation:
                print("✓ インデントが保持されています")
            else:
                print("⚠ インデントが見つかりません")
            
            # 変数の置換確認
            if '{title}' not in report and 'テストタスク' in report:
                print("✓ 変数の置換が正常に動作しています")
            else:
                print("⚠ 変数の置換に問題があります")
                
            print(f"生成されたレポート（最初の3行）:")
            for i, line in enumerate(lines[:3]):
                if line.strip():
                    print(f"  {i+1}: '{line}'")
                    
        except Exception as e:
            print(f"✗ エラー: {e}")

def test_custom_template_loading():
    """カスタムテンプレートの読み込みテスト"""
    print("\n=== カスタムテンプレート読み込みテスト ===")
    
    # カスタムテンプレートファイルのパス
    custom_templates_path = os.path.join("config", "custom_templates.json")
    
    if os.path.exists(custom_templates_path):
        print("✓ カスタムテンプレートファイルが存在します")
        
        try:
            with open(custom_templates_path, 'r', encoding='utf-8') as f:
                custom_templates = json.load(f)
            
            print(f"✓ {len(custom_templates)}個のカスタムテンプレートを読み込みました")
            
            for name, template in custom_templates.items():
                print(f"  - {name}")
                
                # 必要なセクションがあるかチェック
                required_sections = [
                    'project_title', 'task_title', 'task_details', 
                    'comment_history_header', 'comment_entry',
                    'task_separator', 'project_separator'
                ]
                
                missing_sections = [section for section in required_sections if section not in template]
                
                if not missing_sections:
                    print(f"    ✓ 全ての必要なセクションが含まれています")
                else:
                    print(f"    ⚠ 不足しているセクション: {missing_sections}")
                    
        except Exception as e:
            print(f"✗ カスタムテンプレートの読み込みエラー: {e}")
    else:
        print("⚠ カスタムテンプレートファイルが存在しません")

def test_template_validation():
    """テンプレートの検証テスト"""
    print("\n=== テンプレート検証テスト ===")
    
    # 不正なテンプレートのテスト
    invalid_template = {
        'project_title': '● {title}\n',  # project_titleで{title}を使用（不正）
        'task_title': '  {index}. {title}\n',
        'task_details': '進捗: {progress}\n',
        'comment_history_header': '履歴:\n',
        'comment_entry': '{timestamp} {text}\n',
        'task_separator': '\n',
        'project_separator': '\n'
    }
    
    print("不正なテンプレート（project_titleで{title}使用）のテスト:")
    
    test_task = create_test_task()
    report_service = ReportService()
    
    try:
        report = report_service.generate_report([test_task], invalid_template)
        print("⚠ エラーが発生しませんでした（予期しない動作）")
        print(f"生成されたレポート: {report[:100]}...")
    except Exception as e:
        print(f"✓ 期待通りエラーが発生しました: {e}")

if __name__ == "__main__":
    print("テンプレート編集機能のテストを開始します...\n")
    
    test_custom_template_loading()
    test_template_format_preservation()
    test_template_validation()
    
    print("\n=== テスト完了 ===")
