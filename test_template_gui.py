#!/usr/bin/env python3
"""
テンプレート編集GUI機能のテストスクリプト
"""

import sys
import os
import json
from datetime import datetime

# プロジェクトのルートディレクトリをパスに追加
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# PyQt5のテスト用インポート
from PyQt5.QtWidgets import QApplication
from PyQt5.QtTest import QTest
from PyQt5.QtCore import Qt

from app.models.task import Task
from app.controllers.report_controller import ReportController
from app.views.dialogs.template_editor_dialog import TemplateEditorDialog

def create_test_tasks():
    """テスト用のタスクリストを作成"""
    tasks = []
    
    # タスク1
    task1 = Task()
    task1.id = 1
    task1.title = "機能実装"
    task1.description = "新機能の実装作業"
    task1.status = "進行中"
    task1.project = "プロジェクトA"
    task1.start_date = datetime(2024, 1, 1)
    task1.due_date = datetime(2024, 1, 15)
    task1.actual_start_date = datetime(2024, 1, 2)
    task1.estimated_time = 16.0
    task1.actual_time = 8.0
    task1.comments = []
    tasks.append(task1)
    
    # タスク2
    task2 = Task()
    task2.id = 2
    task2.title = "テスト作成"
    task2.description = "単体テストの作成"
    task2.status = "完了"
    task2.project = "プロジェクトA"
    task2.start_date = datetime(2024, 1, 10)
    task2.due_date = datetime(2024, 1, 20)
    task2.actual_start_date = datetime(2024, 1, 10)
    task2.actual_end_date = datetime(2024, 1, 18)
    task2.estimated_time = 8.0
    task2.actual_time = 6.0
    task2.comments = []
    tasks.append(task2)
    
    return tasks

def test_report_controller_integration():
    """ReportControllerとの統合テスト"""
    print("=== ReportController統合テスト ===")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # テスト用タスクを作成
        test_tasks = create_test_tasks()
        
        # ReportControllerを初期化
        controller = ReportController(test_tasks)
        
        print("✓ ReportControllerの初期化成功")
        
        # テンプレートリストの確認
        template_combo = controller.view.template_combo
        template_count = template_combo.count()
        
        if template_count > 0:
            print(f"✓ {template_count}個のテンプレートが読み込まれました")
            
            # 各テンプレートをテスト
            for i in range(template_count):
                template_name = template_combo.itemText(i)
                print(f"  - {template_name}")
        else:
            print("⚠ テンプレートが読み込まれていません")
        
        # テンプレート編集ボタンの存在確認
        edit_btn = controller.view.edit_template_btn
        if edit_btn:
            print("✓ テンプレート編集ボタンが存在します")
            
            # ボタンが有効かチェック
            if edit_btn.isEnabled():
                print("✓ テンプレート編集ボタンが有効です")
            else:
                print("⚠ テンプレート編集ボタンが無効です")
        else:
            print("✗ テンプレート編集ボタンが見つかりません")
        
        # edit_templatesメソッドの存在確認
        if hasattr(controller, 'edit_templates'):
            print("✓ edit_templatesメソッドが実装されています")
        else:
            print("✗ edit_templatesメソッドが実装されていません")
        
        print("✓ ReportController統合テスト完了")
        
    except Exception as e:
        print(f"✗ ReportController統合テストエラー: {e}")
        import traceback
        traceback.print_exc()

def test_template_editor_dialog():
    """TemplateEditorDialogのテスト"""
    print("\n=== TemplateEditorDialog テスト ===")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # TemplateEditorDialogを初期化
        dialog = TemplateEditorDialog()
        
        print("✓ TemplateEditorDialogの初期化成功")
        
        # テンプレートリストの確認
        template_list = dialog.template_list
        template_count = template_list.count()
        
        if template_count > 0:
            print(f"✓ {template_count}個のテンプレートがリストに表示されています")
            
            # 最初のテンプレートを選択してテスト
            first_item = template_list.item(0)
            if first_item:
                template_list.setCurrentItem(first_item)
                
                # エディタにコンテンツが表示されるかチェック
                editor_content = dialog.editor.toPlainText()
                if editor_content:
                    print("✓ エディタにテンプレート内容が表示されています")
                    
                    # JSONフォーマットかチェック
                    try:
                        json.loads(editor_content)
                        print("✓ エディタの内容は有効なJSONフォーマットです")
                    except json.JSONDecodeError:
                        print("⚠ エディタの内容がJSONフォーマットではありません")
                else:
                    print("⚠ エディタにコンテンツが表示されていません")
        else:
            print("⚠ テンプレートリストが空です")
        
        # 変数挿入ボタンの確認
        variable_buttons = dialog.findChildren(type(dialog.new_template_btn))
        variable_button_count = len([btn for btn in variable_buttons if btn.text().startswith('{')])
        
        if variable_button_count > 0:
            print(f"✓ {variable_button_count}個の変数挿入ボタンが存在します")
        else:
            print("⚠ 変数挿入ボタンが見つかりません")
        
        print("✓ TemplateEditorDialog テスト完了")
        
    except Exception as e:
        print(f"✗ TemplateEditorDialog テストエラー: {e}")
        import traceback
        traceback.print_exc()

def test_template_editing_workflow():
    """テンプレート編集ワークフローのテスト"""
    print("\n=== テンプレート編集ワークフロー テスト ===")
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    try:
        # 1. ReportControllerを初期化
        test_tasks = create_test_tasks()
        controller = ReportController(test_tasks)
        
        # 2. 初期レポートの生成確認
        initial_report = controller.view.preview_text.toPlainText()
        if initial_report:
            print("✓ 初期レポートが生成されています")
        else:
            print("⚠ 初期レポートが生成されていません")
        
        # 3. テンプレート変更のテスト
        template_combo = controller.view.template_combo
        if template_combo.count() > 1:
            # 2番目のテンプレートに変更
            template_combo.setCurrentIndex(1)
            
            # レポートが更新されるかチェック
            updated_report = controller.view.preview_text.toPlainText()
            if updated_report != initial_report:
                print("✓ テンプレート変更時にレポートが更新されています")
            else:
                print("⚠ テンプレート変更時にレポートが更新されていません")
        
        print("✓ テンプレート編集ワークフロー テスト完了")
        
    except Exception as e:
        print(f"✗ テンプレート編集ワークフロー テストエラー: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("テンプレート編集GUI機能のテストを開始します...\n")
    
    test_report_controller_integration()
    test_template_editor_dialog()
    test_template_editing_workflow()
    
    print("\n=== 全テスト完了 ===")
    
    # アプリケーションを終了
    app = QApplication.instance()
    if app:
        app.quit()
