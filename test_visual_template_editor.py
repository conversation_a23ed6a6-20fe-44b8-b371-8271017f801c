#!/usr/bin/env python3
"""
ビジュアルテンプレートエディターのテストスクリプト
新しいユーザーフレンドリーなテンプレート編集インターフェースをテストします。
"""

import sys
import os

# プロジェクトルートをパスに追加
sys.path.insert(0, os.path.abspath('.'))

from PyQt5.QtWidgets import QApplication
from app.views.dialogs.template_editor_dialog import TemplateEditorDialog

def test_visual_template_editor():
    """ビジュアルテンプレートエディターをテスト"""
    print("=== ビジュアルテンプレートエディターテスト ===")
    
    try:
        # QApplicationを作成
        app = QApplication(sys.argv)
        
        print("✅ QApplication作成成功")
        
        # テンプレートエディターダイアログを作成
        dialog = TemplateEditorDialog()
        
        print("✅ TemplateEditorDialog作成成功")
        print("📝 新しいビジュアルエディターインターフェースが利用可能です")
        print("🎨 ユーザーフレンドリーなテンプレート編集機能:")
        print("   - 視覚的なセクション編集")
        print("   - ワンクリック変数挿入")
        print("   - リアルタイムプレビュー")
        print("   - JSONエディター（上級者向け）")
        
        # ダイアログを表示
        dialog.show()
        
        print("\n🚀 テンプレートエディターを起動しました")
        print("💡 使い方:")
        print("   1. 左側からテンプレートを選択")
        print("   2. 'ビジュアルエディター'タブで直感的に編集")
        print("   3. 各セクションに文章を入力し、変数ボタンをクリックして挿入")
        print("   4. 'プレビューを表示'でサンプル出力を確認")
        print("   5. '保存'ボタンでテンプレートを保存")
        print("   6. 上級者は'JSONエディター'タブで直接編集可能")
        
        # アプリケーションを実行
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ エラーが発生しました: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_visual_template_editor()
